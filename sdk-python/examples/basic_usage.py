#!/usr/bin/env python3
"""
晨羽智云 Python SDK - 基本使用示例

这个示例展示了如何使用晨羽智云Python SDK进行基本操作。
"""

from chenyu_ai import ChenYuClient
from chenyu_ai.types import *
from chenyu_ai.exceptions import ChenYuAPIError, ChenYuHTTPError


def main():
    # 创建客户端
    client = ChenYuClient(
        base_url="https://www.chenyu.cn/api/v1",
        token="your-token-here"  # 请替换为您的实际Token
    )
    
    # 设置超时时间（可选）
    client.set_timeout(60)
    
    print("=== 晨羽智云 Python SDK 基本使用示例 ===")
    
    try:
        # 1. 查询账户余额
        print("\n1. 查询账户余额...")
        balance_resp = client.get_balance(BalanceRequest())
        print(f"总余额: {balance_resp.balance}")
        print(f"算力卡数量: {len(balance_resp.cards)}")
        for card in balance_resp.cards:
            print(f"卡号: {card.card_no}, 余额: {card.leave_amount}, 状态: {card.status_txt}")
        
        # 2. 查询可用镜像
        print("\n2. 查询可用镜像...")
        pods_resp = client.get_pods(PodListRequest(
            page=1,
            page_size=5,
            kw="jupyter"  # 搜索关键词
        ))
        print(f"找到 {pods_resp.total} 个镜像")
        for pod in pods_resp.items:
            print(f"镜像: {pod.title} ({pod.uuid})")
        
        # 3. 查询GPU资源
        if pods_resp.items:
            print("\n3. 查询GPU资源...")
            selected_pod = pods_resp.items[0]
            gpu_resp = client.get_gpu_models(GpuModelsRequest(
                pod_uuid=selected_pod.uuid
            ))
            print(f"找到 {gpu_resp.total} 个GPU资源")
            for gpu in gpu_resp.items:
                print(f"GPU: {gpu.title}, 价格: {gpu.price}, 可用性: {gpu.free_txt}")
        
        # 4. 查询实例列表
        print("\n4. 查询实例列表...")
        instances_resp = client.get_instances(InstanceListRequest(
            page=1,
            page_size=5
        ))
        print(f"找到 {instances_resp.total} 个实例")
        for instance in instances_resp.instance:
            print(f"实例: {instance.title} ({instance.uuid}), 状态: {instance.status_txt}")
        
        # 5. 查询充值记录
        print("\n5. 查询充值记录...")
        recharge_resp = client.get_recharge(RechargeListRequest(
            page=1,
            page_size=5
        ))
        print(f"找到 {recharge_resp.total} 条充值记录")
        for recharge in recharge_resp.items:
            print(f"充值: {recharge.amount}, 渠道: {recharge.gateway}, 时间: {recharge.created_at}")

        # 6. 查询账单记录
        print("\n6. 查询账单记录...")
        bill_resp = client.get_bill(BillListRequest(
            page=1,
            page_size=5
        ))
        print(f"找到 {bill_resp.total} 条账单记录")
        for bill in bill_resp.items:
            print(f"账单: {bill.occurred_amount}, 卡片: {bill.card_txt}, 时间: {bill.created_at}")
        
        print("\n=== 基本使用示例完成 ===")
        
    except ChenYuHTTPError as e:
        print(f"HTTP请求错误: {e}")
        print(f"状态码: {e.status_code}")
        print(f"响应内容: {e.response_text}")
    except ChenYuAPIError as e:
        print(f"API错误: {e}")
        print(f"错误代码: {e.code}")
        print(f"错误信息: {e.message}")
    except Exception as e:
        print(f"未知错误: {e}")


if __name__ == "__main__":
    main()
