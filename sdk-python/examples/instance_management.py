#!/usr/bin/env python3
"""
晨羽智云 Python SDK - 实例管理示例

这个示例展示了如何使用晨羽智云Python SDK进行实例管理操作。
"""

import time
from chenyu_ai import ChenYuClient
from chenyu_ai.types import *
from chenyu_ai.exceptions import ChenYuAPIError, ChenYuHTTPError


def main():
    # 创建客户端
    client = ChenYuClient(
        base_url="https://www.chenyu.cn/api/v1",
        token="sk-kyhHuxOoAry36cgX1mZTWSWEAet16GsVtz5HNzODql"
    )
    
    print("=== 实例管理示例 ===")
    
    instance_uuid = "c1ce8c227f1946918dd87c9d4a7632d0"
    
    try:
        # 1. 查询可用镜像（注释掉的部分，可以根据需要启用）
        # print("1. 查询可用镜像...")
        # pods_resp = client.get_pods(PodListRequest(
        #     page=1,
        #     page_size=1
        # ))
        # if not pods_resp.items:
        #     print("没有可用的镜像")
        #     return
        # selected_pod = pods_resp.items[0]
        # print(f"选择镜像: {selected_pod.title} ({selected_pod.uuid})")
        
        # 2. 查询GPU资源（注释掉的部分，可以根据需要启用）
        # print("\n2. 查询GPU资源...")
        # gpu_resp = client.get_gpu_models(GpuModelsRequest(
        #     pod_uuid=selected_pod.uuid
        # ))
        # if not gpu_resp.items:
        #     print("没有可用的GPU资源")
        #     return
        # 
        # selected_gpu = None
        # for gpu in gpu_resp.items:
        #     if gpu.free_txt == "充足":
        #         selected_gpu = gpu
        #         break
        # 
        # if not selected_gpu:
        #     print("没有充足的GPU资源")
        #     return
        # print(f"选择GPU: {selected_gpu.title} ({selected_gpu.uuid})")
        
        # 3. 创建实例（注释掉的部分，可以根据需要启用）
        # print("\n3. 创建实例...")
        # create_resp = client.create_instance(InstanceCreateRequest(
        #     pod_uuid=selected_pod.uuid,
        #     gpu_model_uuid=selected_gpu.uuid,
        #     auto_start=1  # 自动启动
        # ))
        # instance_uuid = create_resp.instance.uuid
        # print(f"实例创建成功: {create_resp.instance.title} ({instance_uuid})")
        
        # 4. 等待实例启动（注释掉的部分，可以根据需要启用）
        # print("\n4. 等待实例启动...")
        # for i in range(30):  # 最多等待30次，每次5秒
        #     try:
        #         status_resp = client.get_instance_status(InstanceStatusRequest(
        #             instance_uuid=instance_uuid
        #         ))
        #         print(f"实例状态: {status_resp.instance.status_txt}")
        #         
        #         # 如果实例已经运行，退出循环
        #         if status_resp.instance.status == 2:  # 假设2表示运行中
        #             print("实例已启动!")
        #             if status_resp.instance.startup_maps:
        #                 print(f"访问信息: {status_resp.instance.startup_maps}")
        #             break
        #         
        #         time.sleep(5)
        #     except ChenYuAPIError as e:
        #         print(f"查询实例状态失败: {e}")
        #         continue
        
        # 5. 重启实例（注释掉的部分，可以根据需要启用）
        # print("重启实例...")
        # try:
        #     client.restart_instance(InstanceActionRequest(
        #         instance_uuid=instance_uuid
        #     ))
        #     print("实例重启成功")
        # except ChenYuAPIError as e:
        #     print(f"重启实例失败: {e}")
        
        # 6. 设置定时关机（注释掉的部分，可以根据需要启用）
        # print("\n5. 设置定时关机...")
        # try:
        #     client.set_shutdown_regular_time(InstanceShutdownRegularTimeRequest(
        #         instance_uuid=instance_uuid,
        #         regular_time="2025-06-06 00:00:00",
        #         cancel=True
        #     ))
        #     print("定时关机设置成功")
        # except ChenYuAPIError as e:
        #     print(f"设置定时关机失败: {e}")
        
        # 7. 演示实例操作
        # print("\n6. 演示实例操作...")
        
        # 停止并销毁实例（注释掉的部分，可以根据需要启用）
        # print("停止实例...")
        # try:
        #     client.stop_instance(InstanceActionRequest(
        #         instance_uuid=instance_uuid
        #     ))
        #     print("实例停止成功")
        # except ChenYuAPIError as e:
        #     print(f"停止实例失败: {e}")
        # 
        # # 等待一段时间
        # time.sleep(10)
        
        # 启动实例
        print("启动实例...")
        try:
            client.start_instance(InstanceActionRequest(
                instance_uuid=instance_uuid
            ))
            print("实例启动成功")
        except ChenYuAPIError as e:
            print(f"启动实例失败: {e}")
        
        print(f"\n实例管理示例完成。实例UUID: {instance_uuid}")
        print("请在控制台中手动删除测试实例。")
        
    except ChenYuHTTPError as e:
        print(f"HTTP请求错误: {e}")
    except ChenYuAPIError as e:
        print(f"API错误: {e}")
    except Exception as e:
        print(f"未知错误: {e}")


if __name__ == "__main__":
    main()
