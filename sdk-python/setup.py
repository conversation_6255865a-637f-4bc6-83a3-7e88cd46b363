#!/usr/bin/env python3

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="chenyu-ai",
    version="1.0.0",
    author="晨羽智云",
    author_email="<EMAIL>",
    description="晨羽智云AI实验室API的官方Python SDK",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/chenyu-ai/sdk-python",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.7",
    install_requires=[
        "requests>=2.25.0",
    ],
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
    },
    keywords="chenyu ai api sdk machine-learning artificial-intelligence",
    project_urls={
        "Bug Reports": "https://github.com/chenyu-ai/sdk-python/issues",
        "Source": "https://github.com/chenyu-ai/sdk-python",
        "Documentation": "https://docs.chenyu.cn",
    },
)
