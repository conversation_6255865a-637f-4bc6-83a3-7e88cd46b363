"""
晨羽智云 Python SDK 客户端
"""

import json
import requests
from typing import Optional, Dict, Any
from datetime import datetime
from decimal import Decimal

from .types import *
from .exceptions import ChenYuAPIError, ChenYuHTTPError, ChenYuValidationError


class ChenYuClient:
    """晨羽智云API客户端"""
    
    def __init__(self, base_url: str, token: str, timeout: int = 30):
        """
        初始化客户端
        
        Args:
            base_url: API基础URL
            token: 认证Token
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}'
        })
    
    def set_timeout(self, timeout: int):
        """设置请求超时时间"""
        self.timeout = timeout
    
    def _post(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        发送POST请求的通用方法
        
        Args:
            endpoint: API端点
            data: 请求数据
            
        Returns:
            响应数据字典
            
        Raises:
            ChenYuHTTPError: HTTP请求错误
            ChenYuAPIError: API业务错误
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.post(
                url,
                json=data,
                timeout=self.timeout
            )
            
            # 检查HTTP状态码
            if response.status_code != 200:
                raise ChenYuHTTPError(
                    response.status_code,
                    f"HTTP request failed",
                    response.text
                )
            
            # 解析JSON响应
            try:
                result = response.json()
            except json.JSONDecodeError as e:
                raise ChenYuHTTPError(
                    response.status_code,
                    f"Invalid JSON response: {e}",
                    response.text
                )
            
            # 检查API响应状态
            if result.get('code', 0) != 0:
                raise ChenYuAPIError(
                    result.get('code', -1),
                    result.get('msg', 'Unknown API error')
                )
            
            return result
            
        except requests.RequestException as e:
            raise ChenYuHTTPError(0, f"Request failed: {e}", "")
    
    def _convert_datetime(self, dt_str: str) -> datetime:
        """转换日期时间字符串为datetime对象"""
        if not dt_str:
            return datetime.min
        try:
            return datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            return datetime.min
    
    def _convert_decimal(self, value: str) -> Decimal:
        """转换字符串为Decimal对象"""
        if not value:
            return Decimal('0')
        try:
            return Decimal(value)
        except (ValueError, TypeError):
            return Decimal('0')
    
    # 镜像相关方法
    def get_pods(self, request: PodListRequest) -> PodListResponse:
        """查询可用镜像列表"""
        # 设置默认分页参数
        if request.page <= 0:
            request.page = 1
        if request.page_size <= 0 or request.page_size > 50:
            request.page_size = 10
        
        data = {
            'page': request.page,
            'page_size': request.page_size
        }
        
        if request.pod_id is not None:
            data['pod_id'] = request.pod_id
        if request.pod_uuid:
            data['pod_uuid'] = request.pod_uuid
        if request.kw:
            data['kw'] = request.kw
        
        result = self._post('/app/list', data)
        
        # 转换响应数据
        result_data = result.get('result', {})
        items = []
        for item_data in result_data.get('items', []):
            items.append(PodItem(
                uuid=item_data.get('uuid', ''),
                title=item_data.get('title', ''),
                image_tags=item_data.get('image_tags', ''),
                image_tag=item_data.get('image_tag', ''),
                pod_name=item_data.get('pod_name', ''),
                secret_key=item_data.get('secret_key', ''),
                status=item_data.get('status', 0),
                status_txt=item_data.get('status_txt', ''),
                created_at=self._convert_datetime(item_data.get('created_at', ''))
            ))
        
        return PodListResponse(
            items=items,
            total=result_data.get('total', 0)
        )
    
    # 实例相关方法
    def create_instance(self, request: InstanceCreateRequest) -> InstanceResponse:
        """创建实例"""
        if not request.pod_uuid:
            raise ChenYuValidationError("pod_uuid is required")
        if not request.gpu_model_uuid:
            raise ChenYuValidationError("gpu_model_uuid is required")
        
        data = {
            'pod_uuid': request.pod_uuid,
            'gpu_model_uuid': request.gpu_model_uuid,
            'auto_start': request.auto_start
        }
        
        if request.image_uuid:
            data['image_uuid'] = request.image_uuid
        if request.image_tag:
            data['image_tag'] = request.image_tag
        
        result = self._post('/app/instance/create', data)
        
        # 转换响应数据
        result_data = result.get('result', {})
        instance_data = result_data.get('instance', {})
        
        instance = self._convert_instance_item(instance_data)
        
        return InstanceResponse(instance=instance)
    
    def _convert_instance_item(self, data: Dict[str, Any]) -> InstanceItem:
        """转换实例数据"""
        return InstanceItem(
            uuid=data.get('uuid', ''),
            title=data.get('title', ''),
            pod_uuid=data.get('pod_uuid', ''),
            pod_title=data.get('pod_title', ''),
            pod_name=data.get('pod_name', ''),
            pod_price=data.get('pod_price', ''),
            pod_startup_elapse=data.get('pod_startup_elapse', 0),
            pod_catalogue=data.get('pod_catalogue', []),
            image_tag=data.get('image_tag', ''),
            image_title=data.get('image_title', ''),
            out_web_url=data.get('out_web_url', ''),
            category=data.get('category', 0),
            gpus=data.get('gpus', 0),
            no_card=data.get('no_card', 0),
            gpu_model_name=data.get('gpu_model_name', ''),
            gpu_model=data.get('gpu_model', {}),
            charging_type=data.get('charging_type', 0),
            charging_type_name=data.get('charging_type_name', ''),
            charging_num=data.get('charging_num', 0),
            price_h=data.get('price_h', ''),
            instance_type_txt=data.get('instance_type_txt', ''),
            current_time=self._convert_datetime(data.get('current_time', '')),
            start_time=self._convert_datetime(data.get('start_time', '')),
            end_time=self._convert_datetime(data.get('end_time', '')),
            startup_time=self._convert_datetime(data.get('startup_time', '')),
            shutdown_time=self._convert_datetime(data.get('shutdown_time', '')),
            shutdown_regular_time=self._convert_datetime(data.get('shutdown_regular_time', '')),
            status=data.get('status', 0),
            status_txt=data.get('status_txt', ''),
            image_status=data.get('image_status', 0),
            image_status_txt=data.get('image_status_txt', ''),
            image_startup_log_time=data.get('image_startup_log_time', ''),
            save_image_status=data.get('save_image_status', 0),
            save_image_status_txt=data.get('save_image_status_txt', ''),
            save_image_status_time=self._convert_datetime(data.get('save_image_status_time', '')),
            startup_txt=data.get('startup_txt', ''),
            startup_log_time=data.get('startup_log_time', ''),
            startup_maps=data.get('startup_maps', ''),
            startup_mark_log=data.get('startup_mark_log', []),
            startup_inst_log=data.get('startup_inst_log', []),
            startup_image_log=data.get('startup_image_log', []),
            task_txt=data.get('task_txt'),
            task_last_time=self._convert_datetime(data.get('task_last_time', '')) if data.get('task_last_time') else None,
            task_percent=data.get('task_percent'),
            class_type=data.get('class_type', 0),
            components=data.get('components')
        )

    def stop_instance(self, request: InstanceActionRequest) -> None:
        """停止实例"""
        if not request.instance_uuid:
            raise ChenYuValidationError("instance_uuid is required")

        data = {'instance_uuid': request.instance_uuid}
        self._post('/app/instance/stop', data)

    def start_instance(self, request: InstanceActionRequest) -> None:
        """启动实例"""
        if not request.instance_uuid:
            raise ChenYuValidationError("instance_uuid is required")

        data = {'instance_uuid': request.instance_uuid}
        self._post('/app/instance/start', data)

    def restart_instance(self, request: InstanceActionRequest) -> None:
        """重启实例"""
        if not request.instance_uuid:
            raise ChenYuValidationError("instance_uuid is required")

        data = {'instance_uuid': request.instance_uuid}
        self._post('/app/instance/restart', data)

    def get_instance_status(self, request: InstanceStatusRequest) -> InstanceStatusResponse:
        """查询实例状态"""
        if not request.instance_uuid:
            raise ChenYuValidationError("instance_uuid is required")

        data = {'instance_uuid': request.instance_uuid}
        result = self._post('/app/instance/status', data)

        # 转换响应数据
        result_data = result.get('result', {})
        instance_data = result_data.get('instance', {})

        instance = InstanceStatusItem(
            uuid=instance_data.get('uuid', ''),
            title=instance_data.get('title', ''),
            image_tag=instance_data.get('image_tag', ''),
            pod_name=instance_data.get('pod_name', ''),
            startup_maps=instance_data.get('startup_maps', ''),
            status=instance_data.get('status', 0),
            status_txt=instance_data.get('status_txt', '')
        )

        return InstanceStatusResponse(instance=instance)

    def set_shutdown_regular_time(self, request: InstanceShutdownRegularTimeRequest) -> None:
        """设置定时关机"""
        if not request.instance_uuid:
            raise ChenYuValidationError("instance_uuid is required")
        if not request.regular_time:
            raise ChenYuValidationError("regular_time is required")

        data = {
            'instance_uuid': request.instance_uuid,
            'regular_time': request.regular_time,
            'cancel': request.cancel
        }
        self._post('/app/instance/scheduled/shutdown', data)

    def get_instances(self, request: InstanceListRequest) -> InstanceListResponse:
        """查询实例列表"""
        # 设置默认分页参数
        if request.page <= 0:
            request.page = 1
        if request.page_size <= 0 or request.page_size > 50:
            request.page_size = 10

        data = {
            'page': request.page,
            'page_size': request.page_size
        }

        if request.status is not None:
            data['status'] = request.status

        result = self._post('/app/instance/list', data)

        # 转换响应数据
        result_data = result.get('result', {})
        instances = []
        for instance_data in result_data.get('instance', []):
            instances.append(self._convert_instance_item(instance_data))

        return InstanceListResponse(
            instance=instances,
            total=result_data.get('total', 0)
        )

    def get_gpu_models(self, request: GpuModelsRequest) -> GpuModelsResponse:
        """查询GPU资源"""
        data = {}
        if request.pod_uuid:
            data['pod_uuid'] = request.pod_uuid

        result = self._post('/gpu/models', data)

        # 转换响应数据
        result_data = result.get('result', {})
        items = []
        for item_data in result_data.get('items', []):
            items.append(GpuModelItem(
                uuid=item_data.get('uuid', ''),
                title=item_data.get('title', ''),
                desc=item_data.get('desc', ''),
                price=item_data.get('price', ''),
                remark=item_data.get('remark', ''),
                status=item_data.get('status', 0),
                status_txt=item_data.get('status_txt', ''),
                created_at=self._convert_datetime(item_data.get('created_at', '')),
                free_txt=item_data.get('free_txt', '')
            ))

        return GpuModelsResponse(
            items=items,
            no_card_price=self._convert_decimal(result_data.get('no_card_price', '0')),
            total=result_data.get('total', 0)
        )

    # 财务相关方法
    def get_balance(self, request: BalanceRequest) -> BalanceResponse:
        """查询余额和算力卡信息"""
        result = self._post('/finances/balance', {})

        # 转换响应数据
        result_data = result.get('result', {})
        items = []
        for item_data in result_data.get('items', []):
            items.append(BalanceItem(
                card_no=item_data.get('card_no', ''),
                balance=self._convert_decimal(item_data.get('balance', '0')),
                status=item_data.get('status', 0),
                status_txt=item_data.get('status_txt', ''),
                created_at=self._convert_datetime(item_data.get('created_at', ''))
            ))

        return BalanceResponse(
            items=items,
            total=result_data.get('total', 0)
        )

    def get_recharge(self, request: RechargeRequest) -> RechargeResponse:
        """查询充值记录"""
        # 设置默认分页参数
        if request.page <= 0:
            request.page = 1
        if request.page_size <= 0 or request.page_size > 50:
            request.page_size = 10

        data = {
            'page': request.page,
            'page_size': request.page_size
        }

        result = self._post('/finances/recharge', data)

        # 转换响应数据
        result_data = result.get('result', {})
        items = []
        for item_data in result_data.get('items', []):
            items.append(RechargeItem(
                uuid=item_data.get('uuid', ''),
                amount=self._convert_decimal(item_data.get('amount', '0')),
                status=item_data.get('status', 0),
                status_txt=item_data.get('status_txt', ''),
                created_at=self._convert_datetime(item_data.get('created_at', '')),
                remark=item_data.get('remark', '')
            ))

        return RechargeResponse(
            items=items,
            total=result_data.get('total', 0)
        )

    def get_bill(self, request: BillRequest) -> BillResponse:
        """查询账单记录"""
        # 设置默认分页参数
        if request.page <= 0:
            request.page = 1
        if request.page_size <= 0 or request.page_size > 50:
            request.page_size = 10

        data = {
            'page': request.page,
            'page_size': request.page_size
        }

        result = self._post('/finances/bill', data)

        # 转换响应数据
        result_data = result.get('result', {})
        items = []
        for item_data in result_data.get('items', []):
            items.append(BillItem(
                uuid=item_data.get('uuid', ''),
                amount=self._convert_decimal(item_data.get('amount', '0')),
                type=item_data.get('type', 0),
                type_txt=item_data.get('type_txt', ''),
                status=item_data.get('status', 0),
                status_txt=item_data.get('status_txt', ''),
                created_at=self._convert_datetime(item_data.get('created_at', '')),
                remark=item_data.get('remark', '')
            ))

        return BillResponse(
            items=items,
            total=result_data.get('total', 0)
        )
