"""
晨羽智云 SDK 异常定义
"""


class ChenYuError(Exception):
    """晨羽智云SDK基础异常类"""
    pass


class ChenYuHTTPError(ChenYuError):
    """HTTP请求异常"""
    
    def __init__(self, status_code: int, message: str, response_text: str = ""):
        self.status_code = status_code
        self.message = message
        self.response_text = response_text
        super().__init__(f"HTTP {status_code}: {message}")


class ChenYuAPIError(ChenYuError):
    """API业务异常"""
    
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message
        super().__init__(f"API Error (code {code}): {message}")


class ChenYuValidationError(ChenYuError):
    """参数验证异常"""
    pass
