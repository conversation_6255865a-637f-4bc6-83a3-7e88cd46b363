"""
晨羽智云 SDK 数据类型定义
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime
from decimal import Decimal


@dataclass
class APIResponse:
    """通用API响应结构"""
    code: int
    msg: str
    result: Any = None


@dataclass
class PaginationRequest:
    """分页请求参数"""
    page: int = 1
    page_size: int = 10


@dataclass
class PaginationResponse:
    """分页响应结构"""
    total: int = 0


# 镜像相关类型
@dataclass
class PodListRequest(PaginationRequest):
    """镜像列表查询请求"""
    pod_id: Optional[int] = None
    pod_uuid: Optional[str] = None
    kw: Optional[str] = None  # 关键词搜索


@dataclass
class PodItem:
    """镜像项"""
    uuid: str
    title: str
    image_tags: str
    image_tag: str
    pod_name: str
    secret_key: str
    status: int
    status_txt: str
    created_at: datetime


@dataclass
class PodListResponse(PaginationResponse):
    """镜像列表查询响应"""
    items: List[PodItem] = field(default_factory=list)


# 实例相关类型
@dataclass
class InstanceCreateRequest:
    """创建实例请求"""
    pod_uuid: str
    gpu_model_uuid: str
    image_uuid: Optional[str] = None
    image_tag: Optional[str] = None
    auto_start: int = 0  # 1-自动启动，0-不自动启动


@dataclass
class InstanceItem:
    """实例项"""
    uuid: str
    title: str
    pod_uuid: str
    pod_title: str
    pod_name: str
    pod_price: str
    pod_startup_elapse: int
    pod_catalogue: List[Dict[str, Any]]
    image_tag: str
    image_title: str
    out_web_url: str
    category: int
    gpus: int
    no_card: int
    gpu_model_name: str
    gpu_model: Dict[str, Any]
    charging_type: int
    charging_type_name: str
    charging_num: int
    price_h: str
    instance_type_txt: str
    current_time: datetime
    start_time: datetime
    end_time: datetime
    startup_time: datetime
    shutdown_time: datetime
    shutdown_regular_time: datetime
    status: int
    status_txt: str
    image_status: int
    image_status_txt: str
    image_startup_log_time: str
    save_image_status: int
    save_image_status_txt: str
    save_image_status_time: datetime
    startup_txt: str
    startup_log_time: str
    startup_maps: str
    startup_mark_log: List[str]
    startup_inst_log: List[str]
    startup_image_log: List[str]
    task_txt: Optional[str] = None
    task_last_time: Optional[datetime] = None
    task_percent: Optional[float] = None
    class_type: int = 0
    components: Optional[Dict[str, Any]] = None


@dataclass
class InstanceResponse:
    """实例响应"""
    instance: InstanceItem


@dataclass
class InstanceActionRequest:
    """实例操作请求（启动、停止、重启）"""
    instance_uuid: str


@dataclass
class InstanceStatusRequest:
    """实例状态查询请求"""
    instance_uuid: str


@dataclass
class InstanceStatusItem:
    """实例状态项"""
    uuid: str
    title: str
    image_tag: str
    pod_name: str
    startup_maps: str
    status: int
    status_txt: str


@dataclass
class InstanceStatusResponse:
    """实例状态查询响应"""
    instance: InstanceStatusItem


@dataclass
class InstanceShutdownRegularTimeRequest:
    """设置定时关机请求"""
    instance_uuid: str
    regular_time: str  # 定时关机时间，格式为"2006-01-02 15:04:05"
    cancel: bool = False  # 是否是取消定时关机


@dataclass
class InstanceListRequest(PaginationRequest):
    """实例列表查询请求"""
    status: Optional[int] = None  # 实例状态过滤


@dataclass
class InstanceListResponse(PaginationResponse):
    """实例列表查询响应"""
    instance: List[InstanceItem] = field(default_factory=list)


# GPU资源相关类型
@dataclass
class GpuModelsRequest:
    """GPU资源查询请求"""
    pod_uuid: Optional[str] = None


@dataclass
class GpuModelItem:
    """GPU资源项"""
    uuid: str
    title: str
    desc: str
    price: str
    remark: str
    status: int
    status_txt: str
    created_at: datetime
    free_txt: str


@dataclass
class GpuModelsResponse:
    """GPU资源查询响应"""
    items: List[GpuModelItem] = field(default_factory=list)
    no_card_price: Decimal = Decimal('0')
    total: int = 0


# 财务相关类型
@dataclass
class BalanceRequest:
    """余额查询请求"""
    pass


@dataclass
class CardResponse:
    """算力卡响应"""
    uuid: str
    title: str
    card_no: str
    valid_days: int
    bind_time: datetime
    expire_date: datetime
    sale_price: Decimal
    face_price: Decimal
    leave_amount: Decimal
    pods: List[Dict[str, Any]]
    remark: str
    status: int
    status_txt: str


@dataclass
class BalanceResponse:
    """余额查询响应"""
    cards: List[CardResponse] = field(default_factory=list)
    balance: Decimal = Decimal('0')


@dataclass
class RechargeListRequest(PaginationRequest):
    """充值记录查询请求"""
    kw: Optional[str] = None  # 关键词搜索


@dataclass
class RechargeItem:
    """充值记录项"""
    created_at: datetime
    pay_time: datetime
    out_trade_no: str
    gateway: str
    pay_channel: str
    amount: Decimal


@dataclass
class RechargeListResponse(PaginationResponse):
    """充值记录查询响应"""
    items: List[RechargeItem] = field(default_factory=list)


@dataclass
class BillListRequest(PaginationRequest):
    """账单查询请求"""
    kw: Optional[str] = None  # 关键词搜索


@dataclass
class BillItem:
    """账单项"""
    order_no: str
    occurred_amount: Decimal
    card_txt: str
    show: str
    created_at: datetime


@dataclass
class BillListResponse(PaginationResponse):
    """账单查询响应"""
    items: List[BillItem] = field(default_factory=list)
