# 晨羽智云 Python SDK

晨羽智云AI实验室API的官方Python SDK，提供简单易用的接口来管理您的AI实例、查询资源和财务信息。

## 功能特性

- 🚀 **实例管理**: 创建、启动、停止、重启AI实例
- 💰 **财务查询**: 查询余额、充值记录、账单信息
- 🖥️ **资源管理**: 查询可用镜像和GPU资源
- 🔒 **安全认证**: 支持Token认证
- 📝 **完整类型**: 提供完整的类型定义和文档
- 🛠️ **错误处理**: 详细的错误信息和处理
- 🐍 **Python友好**: 支持Python 3.7+，使用dataclass和类型提示

## 安装

### 使用pip安装

```bash
pip install chenyu-ai
```

### 从源码安装

```bash
git clone https://github.com/chenyu-ai/sdk-python.git
cd sdk-python
pip install -e .
```

## 快速开始

### 1. 创建客户端

```python
from chenyu_ai import ChenYuClient
from chenyu_ai.types import *

# 创建API客户端
client = ChenYuClient(
    base_url="https://api.chenyu.cn/api/v1",
    token="your-token-here"
)

# 设置超时时间（可选）
client.set_timeout(60)
```

### 2. 查询余额

```python
# 查询账户余额和算力卡信息
balance_resp = client.get_balance(BalanceRequest())

print(f"算力卡数量: {balance_resp.total}")
for card in balance_resp.items:
    print(f"卡号: {card.card_no}, 余额: {card.balance}")
```

### 3. 查询可用镜像

```python
# 查询可用的AI镜像
pods_resp = client.get_pods(PodListRequest(
    page=1,
    page_size=10,
    kw="jupyter"  # 搜索关键词
))

for pod in pods_resp.items:
    print(f"镜像: {pod.title} ({pod.uuid})")
```

### 4. 创建和管理实例

```python
# 创建实例
create_resp = client.create_instance(InstanceCreateRequest(
    pod_uuid="pod-uuid-here",
    gpu_model_uuid="gpu-model-uuid-here",
    auto_start=1  # 自动启动
))

instance_uuid = create_resp.instance.uuid
print(f"实例创建成功: {instance_uuid}")

# 查询实例状态
status_resp = client.get_instance_status(InstanceStatusRequest(
    instance_uuid=instance_uuid
))

print(f"实例状态: {status_resp.instance.status_txt}")

# 停止实例
client.stop_instance(InstanceActionRequest(
    instance_uuid=instance_uuid
))
```

## API 参考

### 财务相关

- `get_balance()` - 查询余额和算力卡信息
- `get_recharge()` - 查询充值记录
- `get_bill()` - 查询账单记录

### 应用管理

- `get_pods()` - 查询可用镜像列表
- `create_instance()` - 创建实例
- `get_instances()` - 查询实例列表
- `get_instance_status()` - 查询实例状态

### 实例操作

- `start_instance()` - 启动实例
- `stop_instance()` - 停止实例
- `restart_instance()` - 重启实例
- `set_shutdown_regular_time()` - 设置定时关机

### 资源查询

- `get_gpu_models()` - 查询GPU资源

## 示例代码

查看 `examples/` 目录下的完整示例：

- `basic_usage.py` - 基本使用示例
- `instance_management.py` - 实例管理示例

## 错误处理

SDK提供详细的错误信息：

```python
from chenyu_ai.exceptions import ChenYuAPIError, ChenYuHTTPError

try:
    resp = client.get_balance(BalanceRequest())
except ChenYuHTTPError as e:
    # 处理HTTP错误
    print(f"HTTP错误: {e.status_code} - {e.message}")
except ChenYuAPIError as e:
    # 处理API业务错误
    print(f"API错误: {e.code} - {e.message}")
```

## 类型支持

SDK使用Python的dataclass和类型提示，提供完整的IDE支持：

```python
from chenyu_ai.types import PodListRequest, InstanceCreateRequest

# 自动补全和类型检查
request = PodListRequest(
    page=1,
    page_size=10,
    kw="jupyter"
)
```

## 认证

使用您的API Token进行认证：

1. 登录晨羽智云控制台
2. 在API管理页面生成Token
3. 在创建客户端时传入Token

```python
client = ChenYuClient("https://api.chenyu.cn/api/v1", "your-token-here")
```

## 开发

### 安装开发依赖

```bash
pip install -e ".[dev]"
```

### 运行测试

```bash
pytest
```

### 代码格式化

```bash
black .
```

### 类型检查

```bash
mypy chenyu_ai
```

## 贡献

欢迎提交Issue和Pull Request来改进这个SDK。

## 许可证

MIT License

## 支持

如有问题，请联系技术支持或查看[官方文档](https://docs.chenyu.cn)。
