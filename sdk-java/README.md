# 晨羽智云 Java SDK

晨羽智云AI实验室API的官方Java SDK，提供简单易用的接口来管理您的AI实例、查询资源和财务信息。

## 功能特性

- 🚀 **实例管理**: 创建、启动、停止、重启AI实例
- 💰 **财务查询**: 查询余额、充值记录、账单信息
- 🖥️ **资源管理**: 查询可用镜像和GPU资源
- 🔒 **安全认证**: 支持Token认证
- 📝 **完整类型**: 提供完整的类型定义和文档
- 🛠️ **错误处理**: 详细的错误信息和处理
- ☕ **Java友好**: 支持Java 8+，使用现代Java特性

## 系统要求

- Java 8 或更高版本
- Maven 3.6+ 或 Gradle 6+

## 安装

### Maven

在您的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>cn.chenyu</groupId>
    <artifactId>chenyu-ai-sdk</artifactId>
    <version>1.0.0</version>
</dependency>
```

### Gradle

在您的 `build.gradle` 中添加依赖：

```gradle
implementation 'cn.chenyu:chenyu-ai-sdk:1.0.0'
```

### 从源码构建

```bash
git clone https://github.com/chenyu-ai/sdk-java.git
cd sdk-java
mvn clean install
```

## 快速开始

### 1. 创建客户端

```java
import cn.chenyu.sdk.ChenYuClient;
import cn.chenyu.sdk.types.*;

// 创建API客户端
ChenYuClient client = new ChenYuClient(
    "https://api.chenyu.cn/api/v1",
    "your-token-here"
);

// 设置超时时间（可选）
client = client.setTimeout(60);
```

### 2. 查询可用镜像

```java
// 查询可用的AI镜像
PodListRequest request = new PodListRequest(1, 10);
request.setKw("jupyter"); // 搜索关键词

PodListResponse response = client.getPods(request);

for (PodItem pod : response.getItems()) {
    System.out.printf("镜像: %s (%s)%n", pod.getTitle(), pod.getUuid());
}
```

### 3. 创建和管理实例

```java
// 创建实例
InstanceCreateRequest createRequest = new InstanceCreateRequest(
    "pod-uuid-here",
    "gpu-model-uuid-here"
);
createRequest.setAutoStart(1); // 自动启动

InstanceResponse createResponse = client.createInstance(createRequest);
String instanceUuid = createResponse.getInstance().getUuid();
System.out.printf("实例创建成功: %s%n", instanceUuid);

// 查询实例状态
InstanceStatusRequest statusRequest = new InstanceStatusRequest(instanceUuid);
InstanceStatusResponse statusResponse = client.getInstanceStatus(statusRequest);
System.out.printf("实例状态: %s%n", statusResponse.getInstance().getStatusTxt());

// 停止实例
InstanceActionRequest stopRequest = new InstanceActionRequest(instanceUuid);
client.stopInstance(stopRequest);
```

## API 参考

### 应用管理

- `getPods()` - 查询可用镜像列表
- `createInstance()` - 创建实例
- `getInstanceStatus()` - 查询实例状态

### 实例操作

- `startInstance()` - 启动实例
- `stopInstance()` - 停止实例
- `restartInstance()` - 重启实例

## 示例代码

查看 `src/main/java/examples/` 目录下的完整示例：

- `BasicUsage.java` - 基本使用示例
- `InstanceManagement.java` - 实例管理示例

## 错误处理

SDK提供详细的错误信息：

```java
import cn.chenyu.sdk.exceptions.*;

try {
    PodListResponse response = client.getPods(request);
} catch (ChenYuHTTPException e) {
    // 处理HTTP错误
    System.out.printf("HTTP错误: %d - %s%n", e.getStatusCode(), e.getMessage());
} catch (ChenYuAPIException e) {
    // 处理API业务错误
    System.out.printf("API错误: %d - %s%n", e.getCode(), e.getApiMessage());
} catch (ChenYuValidationException e) {
    // 处理参数验证错误
    System.out.printf("参数错误: %s%n", e.getMessage());
}
```

## 认证

使用您的API Token进行认证：

1. 登录晨羽智云控制台
2. 在API管理页面生成Token
3. 在创建客户端时传入Token

```java
ChenYuClient client = new ChenYuClient("https://api.chenyu.cn/api/v1", "your-token-here");
```

## 开发

### 构建项目

```bash
mvn clean compile
```

### 运行测试

```bash
mvn test
```

### 生成文档

```bash
mvn javadoc:javadoc
```

### 打包

```bash
mvn clean package
```

## 依赖

- [OkHttp](https://square.github.io/okhttp/) - HTTP客户端
- [Jackson](https://github.com/FasterXML/jackson) - JSON处理
- [JUnit 5](https://junit.org/junit5/) - 测试框架（仅测试时）

## 贡献

欢迎提交Issue和Pull Request来改进这个SDK。

## 许可证

MIT License

## 支持

如有问题，请联系技术支持或查看[官方文档](https://docs.chenyu.cn)。
