package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.ArrayList;

/**
 * 账单查询响应
 */
public class BillListResponse extends PaginationResponse {
    
    @JsonProperty("items")
    private List<BillItem> items = new ArrayList<>();
    
    public BillListResponse() {}
    
    public BillListResponse(List<BillItem> items, int total) {
        super(total);
        this.items = items;
    }
    
    public List<BillItem> getItems() {
        return items;
    }
    
    public void setItems(List<BillItem> items) {
        this.items = items;
    }
}
