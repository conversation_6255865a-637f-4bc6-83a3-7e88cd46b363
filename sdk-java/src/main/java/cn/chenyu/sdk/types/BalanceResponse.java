package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;

/**
 * 余额查询响应
 */
public class BalanceResponse {
    
    @JsonProperty("cards")
    private List<CardResponse> cards = new ArrayList<>();
    
    @JsonProperty("balance")
    private BigDecimal balance = BigDecimal.ZERO;
    
    public BalanceResponse() {}
    
    public BalanceResponse(List<CardResponse> cards, BigDecimal balance) {
        this.cards = cards;
        this.balance = balance;
    }
    
    public List<CardResponse> getCards() {
        return cards;
    }
    
    public void setCards(List<CardResponse> cards) {
        this.cards = cards;
    }
    
    public BigDecimal getBalance() {
        return balance;
    }
    
    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }
}
