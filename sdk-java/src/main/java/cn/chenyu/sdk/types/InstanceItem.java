package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 实例项（简化版本，包含主要字段）
 */
public class InstanceItem {
    
    @JsonProperty("uuid")
    private String uuid;
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("pod_uuid")
    private String podUuid;
    
    @JsonProperty("pod_title")
    private String podTitle;
    
    @JsonProperty("pod_name")
    private String podName;
    
    @JsonProperty("pod_price")
    private String podPrice;
    
    @JsonProperty("image_tag")
    private String imageTag;
    
    @JsonProperty("image_title")
    private String imageTitle;
    
    @JsonProperty("out_web_url")
    private String outWebUrl;
    
    @JsonProperty("category")
    private int category;
    
    @JsonProperty("gpus")
    private int gpus;
    
    @JsonProperty("gpu_model_name")
    private String gpuModelName;
    
    @JsonProperty("charging_type")
    private int chargingType;
    
    @JsonProperty("charging_type_name")
    private String chargingTypeName;
    
    @JsonProperty("price_h")
    private String priceH;
    
    @JsonProperty("instance_type_txt")
    private String instanceTypeTxt;
    
    @JsonProperty("current_time")
    private LocalDateTime currentTime;
    
    @JsonProperty("start_time")
    private LocalDateTime startTime;
    
    @JsonProperty("end_time")
    private LocalDateTime endTime;
    
    @JsonProperty("startup_time")
    private LocalDateTime startupTime;
    
    @JsonProperty("shutdown_time")
    private LocalDateTime shutdownTime;
    
    @JsonProperty("shutdown_regular_time")
    private LocalDateTime shutdownRegularTime;
    
    @JsonProperty("status")
    private int status;
    
    @JsonProperty("status_txt")
    private String statusTxt;
    
    @JsonProperty("image_status")
    private int imageStatus;
    
    @JsonProperty("image_status_txt")
    private String imageStatusTxt;
    
    @JsonProperty("startup_txt")
    private String startupTxt;
    
    @JsonProperty("startup_maps")
    private String startupMaps;
    
    @JsonProperty("startup_mark_log")
    private List<String> startupMarkLog;
    
    @JsonProperty("startup_inst_log")
    private List<String> startupInstLog;
    
    @JsonProperty("startup_image_log")
    private List<String> startupImageLog;
    
    @JsonProperty("task_txt")
    private String taskTxt;
    
    @JsonProperty("task_last_time")
    private LocalDateTime taskLastTime;
    
    @JsonProperty("task_percent")
    private Double taskPercent;
    
    @JsonProperty("class_type")
    private int classType;
    
    @JsonProperty("components")
    private Map<String, Object> components;
    
    public InstanceItem() {}
    
    // Getters and Setters
    public String getUuid() { return uuid; }
    public void setUuid(String uuid) { this.uuid = uuid; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getPodUuid() { return podUuid; }
    public void setPodUuid(String podUuid) { this.podUuid = podUuid; }
    
    public String getPodTitle() { return podTitle; }
    public void setPodTitle(String podTitle) { this.podTitle = podTitle; }
    
    public String getPodName() { return podName; }
    public void setPodName(String podName) { this.podName = podName; }
    
    public String getPodPrice() { return podPrice; }
    public void setPodPrice(String podPrice) { this.podPrice = podPrice; }
    
    public String getImageTag() { return imageTag; }
    public void setImageTag(String imageTag) { this.imageTag = imageTag; }
    
    public String getImageTitle() { return imageTitle; }
    public void setImageTitle(String imageTitle) { this.imageTitle = imageTitle; }
    
    public String getOutWebUrl() { return outWebUrl; }
    public void setOutWebUrl(String outWebUrl) { this.outWebUrl = outWebUrl; }
    
    public int getCategory() { return category; }
    public void setCategory(int category) { this.category = category; }
    
    public int getGpus() { return gpus; }
    public void setGpus(int gpus) { this.gpus = gpus; }
    
    public String getGpuModelName() { return gpuModelName; }
    public void setGpuModelName(String gpuModelName) { this.gpuModelName = gpuModelName; }
    
    public int getChargingType() { return chargingType; }
    public void setChargingType(int chargingType) { this.chargingType = chargingType; }
    
    public String getChargingTypeName() { return chargingTypeName; }
    public void setChargingTypeName(String chargingTypeName) { this.chargingTypeName = chargingTypeName; }
    
    public String getPriceH() { return priceH; }
    public void setPriceH(String priceH) { this.priceH = priceH; }
    
    public String getInstanceTypeTxt() { return instanceTypeTxt; }
    public void setInstanceTypeTxt(String instanceTypeTxt) { this.instanceTypeTxt = instanceTypeTxt; }
    
    public LocalDateTime getCurrentTime() { return currentTime; }
    public void setCurrentTime(LocalDateTime currentTime) { this.currentTime = currentTime; }
    
    public LocalDateTime getStartTime() { return startTime; }
    public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
    
    public LocalDateTime getEndTime() { return endTime; }
    public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
    
    public LocalDateTime getStartupTime() { return startupTime; }
    public void setStartupTime(LocalDateTime startupTime) { this.startupTime = startupTime; }
    
    public LocalDateTime getShutdownTime() { return shutdownTime; }
    public void setShutdownTime(LocalDateTime shutdownTime) { this.shutdownTime = shutdownTime; }
    
    public LocalDateTime getShutdownRegularTime() { return shutdownRegularTime; }
    public void setShutdownRegularTime(LocalDateTime shutdownRegularTime) { this.shutdownRegularTime = shutdownRegularTime; }
    
    public int getStatus() { return status; }
    public void setStatus(int status) { this.status = status; }
    
    public String getStatusTxt() { return statusTxt; }
    public void setStatusTxt(String statusTxt) { this.statusTxt = statusTxt; }
    
    public int getImageStatus() { return imageStatus; }
    public void setImageStatus(int imageStatus) { this.imageStatus = imageStatus; }
    
    public String getImageStatusTxt() { return imageStatusTxt; }
    public void setImageStatusTxt(String imageStatusTxt) { this.imageStatusTxt = imageStatusTxt; }
    
    public String getStartupTxt() { return startupTxt; }
    public void setStartupTxt(String startupTxt) { this.startupTxt = startupTxt; }
    
    public String getStartupMaps() { return startupMaps; }
    public void setStartupMaps(String startupMaps) { this.startupMaps = startupMaps; }
    
    public List<String> getStartupMarkLog() { return startupMarkLog; }
    public void setStartupMarkLog(List<String> startupMarkLog) { this.startupMarkLog = startupMarkLog; }
    
    public List<String> getStartupInstLog() { return startupInstLog; }
    public void setStartupInstLog(List<String> startupInstLog) { this.startupInstLog = startupInstLog; }
    
    public List<String> getStartupImageLog() { return startupImageLog; }
    public void setStartupImageLog(List<String> startupImageLog) { this.startupImageLog = startupImageLog; }
    
    public String getTaskTxt() { return taskTxt; }
    public void setTaskTxt(String taskTxt) { this.taskTxt = taskTxt; }
    
    public LocalDateTime getTaskLastTime() { return taskLastTime; }
    public void setTaskLastTime(LocalDateTime taskLastTime) { this.taskLastTime = taskLastTime; }
    
    public Double getTaskPercent() { return taskPercent; }
    public void setTaskPercent(Double taskPercent) { this.taskPercent = taskPercent; }
    
    public int getClassType() { return classType; }
    public void setClassType(int classType) { this.classType = classType; }
    
    public Map<String, Object> getComponents() { return components; }
    public void setComponents(Map<String, Object> components) { this.components = components; }
}
