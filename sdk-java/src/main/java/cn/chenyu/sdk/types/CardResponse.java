package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 算力卡响应
 */
public class CardResponse {
    
    @JsonProperty("uuid")
    private String uuid;
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("card_no")
    private String cardNo;
    
    @JsonProperty("valid_days")
    private int validDays;
    
    @JsonProperty("bind_time")
    private LocalDateTime bindTime;
    
    @JsonProperty("expire_date")
    private LocalDateTime expireDate;
    
    @JsonProperty("sale_price")
    private BigDecimal salePrice;
    
    @JsonProperty("face_price")
    private BigDecimal facePrice;
    
    @JsonProperty("leave_amount")
    private BigDecimal leaveAmount;
    
    @JsonProperty("pods")
    private List<Map<String, Object>> pods;
    
    @JsonProperty("remark")
    private String remark;
    
    @JsonProperty("status")
    private int status;
    
    @JsonProperty("status_txt")
    private String statusTxt;
    
    public CardResponse() {}
    
    // Getters and Setters
    public String getUuid() { return uuid; }
    public void setUuid(String uuid) { this.uuid = uuid; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getCardNo() { return cardNo; }
    public void setCardNo(String cardNo) { this.cardNo = cardNo; }
    
    public int getValidDays() { return validDays; }
    public void setValidDays(int validDays) { this.validDays = validDays; }
    
    public LocalDateTime getBindTime() { return bindTime; }
    public void setBindTime(LocalDateTime bindTime) { this.bindTime = bindTime; }
    
    public LocalDateTime getExpireDate() { return expireDate; }
    public void setExpireDate(LocalDateTime expireDate) { this.expireDate = expireDate; }
    
    public BigDecimal getSalePrice() { return salePrice; }
    public void setSalePrice(BigDecimal salePrice) { this.salePrice = salePrice; }
    
    public BigDecimal getFacePrice() { return facePrice; }
    public void setFacePrice(BigDecimal facePrice) { this.facePrice = facePrice; }
    
    public BigDecimal getLeaveAmount() { return leaveAmount; }
    public void setLeaveAmount(BigDecimal leaveAmount) { this.leaveAmount = leaveAmount; }
    
    public List<Map<String, Object>> getPods() { return pods; }
    public void setPods(List<Map<String, Object>> pods) { this.pods = pods; }
    
    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }
    
    public int getStatus() { return status; }
    public void setStatus(int status) { this.status = status; }
    
    public String getStatusTxt() { return statusTxt; }
    public void setStatusTxt(String statusTxt) { this.statusTxt = statusTxt; }
}
