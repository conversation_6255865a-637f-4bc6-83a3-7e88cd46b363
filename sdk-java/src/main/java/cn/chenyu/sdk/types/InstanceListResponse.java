package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.ArrayList;

/**
 * 实例列表查询响应
 */
public class InstanceListResponse extends PaginationResponse {
    
    @JsonProperty("instance")
    private List<InstanceItem> instance = new ArrayList<>();
    
    public InstanceListResponse() {}
    
    public InstanceListResponse(List<InstanceItem> instance, int total) {
        super(total);
        this.instance = instance;
    }
    
    public List<InstanceItem> getInstance() {
        return instance;
    }
    
    public void setInstance(List<InstanceItem> instance) {
        this.instance = instance;
    }
}
