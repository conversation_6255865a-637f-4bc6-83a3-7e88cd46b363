package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 分页请求参数
 */
public class PaginationRequest {
    
    @JsonProperty("page")
    private int page = 1;
    
    @JsonProperty("page_size")
    private int pageSize = 10;
    
    public PaginationRequest() {}
    
    public PaginationRequest(int page, int pageSize) {
        this.page = page;
        this.pageSize = pageSize;
    }
    
    public int getPage() {
        return page;
    }
    
    public void setPage(int page) {
        this.page = page;
    }
    
    public int getPageSize() {
        return pageSize;
    }
    
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
