package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值记录项
 */
public class RechargeItem {
    
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    @JsonProperty("pay_time")
    private LocalDateTime payTime;
    
    @JsonProperty("out_trade_no")
    private String outTradeNo;
    
    @JsonProperty("gateway")
    private String gateway;
    
    @JsonProperty("pay_channel")
    private String payChannel;
    
    @JsonProperty("amount")
    private BigDecimal amount;
    
    public RechargeItem() {}
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getPayTime() {
        return payTime;
    }
    
    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }
    
    public String getOutTradeNo() {
        return outTradeNo;
    }
    
    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }
    
    public String getGateway() {
        return gateway;
    }
    
    public void setGateway(String gateway) {
        this.gateway = gateway;
    }
    
    public String getPayChannel() {
        return payChannel;
    }
    
    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }
    
    public BigDecimal getAmount() {
        return amount;
    }
    
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}
