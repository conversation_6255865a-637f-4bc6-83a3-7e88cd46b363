package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 镜像列表查询请求
 */
public class PodListRequest extends PaginationRequest {
    
    @JsonProperty("pod_id")
    private Integer podId;
    
    @JsonProperty("pod_uuid")
    private String podUuid;
    
    @JsonProperty("kw")
    private String kw; // 关键词搜索
    
    public PodListRequest() {}
    
    public PodListRequest(int page, int pageSize) {
        super(page, pageSize);
    }
    
    public Integer getPodId() {
        return podId;
    }
    
    public void setPodId(Integer podId) {
        this.podId = podId;
    }
    
    public String getPodUuid() {
        return podUuid;
    }
    
    public void setPodUuid(String podUuid) {
        this.podUuid = podUuid;
    }
    
    public String getKw() {
        return kw;
    }
    
    public void setKw(String kw) {
        this.kw = kw;
    }
}
