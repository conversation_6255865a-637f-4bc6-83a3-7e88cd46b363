package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.ArrayList;

/**
 * 充值记录查询响应
 */
public class RechargeListResponse extends PaginationResponse {
    
    @JsonProperty("items")
    private List<RechargeItem> items = new ArrayList<>();
    
    public RechargeListResponse() {}
    
    public RechargeListResponse(List<RechargeItem> items, int total) {
        super(total);
        this.items = items;
    }
    
    public List<RechargeItem> getItems() {
        return items;
    }
    
    public void setItems(List<RechargeItem> items) {
        this.items = items;
    }
}
