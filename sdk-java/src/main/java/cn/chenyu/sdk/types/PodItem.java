package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;

/**
 * 镜像项
 */
public class PodItem {
    
    @JsonProperty("uuid")
    private String uuid;
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("image_tags")
    private String imageTags;
    
    @JsonProperty("image_tag")
    private String imageTag;
    
    @JsonProperty("pod_name")
    private String podName;
    
    @JsonProperty("secret_key")
    private String secretKey;
    
    @JsonProperty("status")
    private int status;
    
    @JsonProperty("status_txt")
    private String statusTxt;
    
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    public PodItem() {}
    
    public String getUuid() {
        return uuid;
    }
    
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getImageTags() {
        return imageTags;
    }
    
    public void setImageTags(String imageTags) {
        this.imageTags = imageTags;
    }
    
    public String getImageTag() {
        return imageTag;
    }
    
    public void setImageTag(String imageTag) {
        this.imageTag = imageTag;
    }
    
    public String getPodName() {
        return podName;
    }
    
    public void setPodName(String podName) {
        this.podName = podName;
    }
    
    public String getSecretKey() {
        return secretKey;
    }
    
    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }
    
    public int getStatus() {
        return status;
    }
    
    public void setStatus(int status) {
        this.status = status;
    }
    
    public String getStatusTxt() {
        return statusTxt;
    }
    
    public void setStatusTxt(String statusTxt) {
        this.statusTxt = statusTxt;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}
