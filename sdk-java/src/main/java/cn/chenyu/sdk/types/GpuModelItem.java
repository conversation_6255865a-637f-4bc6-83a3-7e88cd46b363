package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;

/**
 * GPU资源项
 */
public class GpuModelItem {
    
    @JsonProperty("uuid")
    private String uuid;
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("desc")
    private String desc;
    
    @JsonProperty("price")
    private String price;
    
    @JsonProperty("remark")
    private String remark;
    
    @JsonProperty("status")
    private int status;
    
    @JsonProperty("status_txt")
    private String statusTxt;
    
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    @JsonProperty("free_txt")
    private String freeTxt;
    
    public GpuModelItem() {}
    
    public String getUuid() {
        return uuid;
    }
    
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public void setDesc(String desc) {
        this.desc = desc;
    }
    
    public String getPrice() {
        return price;
    }
    
    public void setPrice(String price) {
        this.price = price;
    }
    
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    public int getStatus() {
        return status;
    }
    
    public void setStatus(int status) {
        this.status = status;
    }
    
    public String getStatusTxt() {
        return statusTxt;
    }
    
    public void setStatusTxt(String statusTxt) {
        this.statusTxt = statusTxt;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public String getFreeTxt() {
        return freeTxt;
    }
    
    public void setFreeTxt(String freeTxt) {
        this.freeTxt = freeTxt;
    }
}
