package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 设置定时关机请求
 */
public class InstanceShutdownRegularTimeRequest {
    
    @JsonProperty("instance_uuid")
    private String instanceUuid;
    
    @JsonProperty("regular_time")
    private String regularTime; // 定时关机时间，格式为"2006-01-02 15:04:05"
    
    @JsonProperty("cancel")
    private boolean cancel = false; // 是否是取消定时关机
    
    public InstanceShutdownRegularTimeRequest() {}
    
    public InstanceShutdownRegularTimeRequest(String instanceUuid, String regularTime) {
        this.instanceUuid = instanceUuid;
        this.regularTime = regularTime;
    }
    
    public String getInstanceUuid() {
        return instanceUuid;
    }
    
    public void setInstanceUuid(String instanceUuid) {
        this.instanceUuid = instanceUuid;
    }
    
    public String getRegularTime() {
        return regularTime;
    }
    
    public void setRegularTime(String regularTime) {
        this.regularTime = regularTime;
    }
    
    public boolean isCancel() {
        return cancel;
    }
    
    public void setCancel(boolean cancel) {
        this.cancel = cancel;
    }
}
