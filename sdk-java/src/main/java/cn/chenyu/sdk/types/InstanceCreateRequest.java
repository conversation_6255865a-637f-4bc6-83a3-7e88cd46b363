package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 创建实例请求
 */
public class InstanceCreateRequest {
    
    @JsonProperty("pod_uuid")
    private String podUuid;
    
    @JsonProperty("gpu_model_uuid")
    private String gpuModelUuid;
    
    @JsonProperty("image_uuid")
    private String imageUuid;
    
    @JsonProperty("image_tag")
    private String imageTag;
    
    @JsonProperty("auto_start")
    private int autoStart = 0; // 1-自动启动，0-不自动启动
    
    public InstanceCreateRequest() {}
    
    public InstanceCreateRequest(String podUuid, String gpuModelUuid) {
        this.podUuid = podUuid;
        this.gpuModelUuid = gpuModelUuid;
    }
    
    public String getPodUuid() {
        return podUuid;
    }
    
    public void setPodUuid(String podUuid) {
        this.podUuid = podUuid;
    }
    
    public String getGpuModelUuid() {
        return gpuModelUuid;
    }
    
    public void setGpuModelUuid(String gpuModelUuid) {
        this.gpuModelUuid = gpuModelUuid;
    }
    
    public String getImageUuid() {
        return imageUuid;
    }
    
    public void setImageUuid(String imageUuid) {
        this.imageUuid = imageUuid;
    }
    
    public String getImageTag() {
        return imageTag;
    }
    
    public void setImageTag(String imageTag) {
        this.imageTag = imageTag;
    }
    
    public int getAutoStart() {
        return autoStart;
    }
    
    public void setAutoStart(int autoStart) {
        this.autoStart = autoStart;
    }
}
