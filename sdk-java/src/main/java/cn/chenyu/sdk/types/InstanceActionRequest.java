package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 实例操作请求（启动、停止、重启）
 */
public class InstanceActionRequest {
    
    @JsonProperty("instance_uuid")
    private String instanceUuid;
    
    public InstanceActionRequest() {}
    
    public InstanceActionRequest(String instanceUuid) {
        this.instanceUuid = instanceUuid;
    }
    
    public String getInstanceUuid() {
        return instanceUuid;
    }
    
    public void setInstanceUuid(String instanceUuid) {
        this.instanceUuid = instanceUuid;
    }
}
