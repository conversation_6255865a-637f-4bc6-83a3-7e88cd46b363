package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 账单项
 */
public class BillItem {
    
    @JsonProperty("order_no")
    private String orderNo;
    
    @JsonProperty("occurred_amount")
    private BigDecimal occurredAmount;
    
    @JsonProperty("card_txt")
    private String cardTxt;
    
    @JsonProperty("show")
    private String show;
    
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    public BillItem() {}
    
    public String getOrderNo() {
        return orderNo;
    }
    
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
    
    public BigDecimal getOccurredAmount() {
        return occurredAmount;
    }
    
    public void setOccurredAmount(BigDecimal occurredAmount) {
        this.occurredAmount = occurredAmount;
    }
    
    public String getCardTxt() {
        return cardTxt;
    }
    
    public void setCardTxt(String cardTxt) {
        this.cardTxt = cardTxt;
    }
    
    public String getShow() {
        return show;
    }
    
    public void setShow(String show) {
        this.show = show;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}
