package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 实例状态项
 */
public class InstanceStatusItem {
    
    @JsonProperty("uuid")
    private String uuid;
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("image_tag")
    private String imageTag;
    
    @JsonProperty("pod_name")
    private String podName;
    
    @JsonProperty("startup_maps")
    private String startupMaps;
    
    @JsonProperty("status")
    private int status;
    
    @JsonProperty("status_txt")
    private String statusTxt;
    
    public InstanceStatusItem() {}
    
    public String getUuid() {
        return uuid;
    }
    
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getImageTag() {
        return imageTag;
    }
    
    public void setImageTag(String imageTag) {
        this.imageTag = imageTag;
    }
    
    public String getPodName() {
        return podName;
    }
    
    public void setPodName(String podName) {
        this.podName = podName;
    }
    
    public String getStartupMaps() {
        return startupMaps;
    }
    
    public void setStartupMaps(String startupMaps) {
        this.startupMaps = startupMaps;
    }
    
    public int getStatus() {
        return status;
    }
    
    public void setStatus(int status) {
        this.status = status;
    }
    
    public String getStatusTxt() {
        return statusTxt;
    }
    
    public void setStatusTxt(String statusTxt) {
        this.statusTxt = statusTxt;
    }
}
