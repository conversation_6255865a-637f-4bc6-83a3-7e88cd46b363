package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 实例列表查询请求
 */
public class InstanceListRequest extends PaginationRequest {
    
    @JsonProperty("status")
    private Integer status; // 实例状态过滤
    
    public InstanceListRequest() {}
    
    public InstanceListRequest(int page, int pageSize) {
        super(page, pageSize);
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
}
