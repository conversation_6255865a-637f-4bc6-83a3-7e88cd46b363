package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.ArrayList;

/**
 * 镜像列表查询响应
 */
public class PodListResponse extends PaginationResponse {
    
    @JsonProperty("items")
    private List<PodItem> items = new ArrayList<>();
    
    public PodListResponse() {}
    
    public PodListResponse(List<PodItem> items, int total) {
        super(total);
        this.items = items;
    }
    
    public List<PodItem> getItems() {
        return items;
    }
    
    public void setItems(List<PodItem> items) {
        this.items = items;
    }
}
