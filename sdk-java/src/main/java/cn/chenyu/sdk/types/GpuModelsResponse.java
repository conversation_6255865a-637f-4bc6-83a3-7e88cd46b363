package cn.chenyu.sdk.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;

/**
 * GPU资源查询响应
 */
public class GpuModelsResponse {
    
    @JsonProperty("items")
    private List<GpuModelItem> items = new ArrayList<>();
    
    @JsonProperty("no_card_price")
    private BigDecimal noCardPrice = BigDecimal.ZERO;
    
    @JsonProperty("total")
    private int total = 0;
    
    public GpuModelsResponse() {}
    
    public GpuModelsResponse(List<GpuModelItem> items, BigDecimal noCardPrice, int total) {
        this.items = items;
        this.noCardPrice = noCardPrice;
        this.total = total;
    }
    
    public List<GpuModelItem> getItems() {
        return items;
    }
    
    public void setItems(List<GpuModelItem> items) {
        this.items = items;
    }
    
    public BigDecimal getNoCardPrice() {
        return noCardPrice;
    }
    
    public void setNoCardPrice(BigDecimal noCardPrice) {
        this.noCardPrice = noCardPrice;
    }
    
    public int getTotal() {
        return total;
    }
    
    public void setTotal(int total) {
        this.total = total;
    }
}
