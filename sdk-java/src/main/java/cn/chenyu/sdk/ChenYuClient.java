package cn.chenyu.sdk;

import cn.chenyu.sdk.exceptions.*;
import cn.chenyu.sdk.types.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import okhttp3.*;

import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 晨羽智云API客户端
 */
public class ChenYuClient {
    
    private final String baseUrl;
    private final String token;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    
    public ChenYuClient(String baseUrl, String token) {
        this(baseUrl, token, 30);
    }
    
    public ChenYuClient(String baseUrl, String token, int timeoutSeconds) {
        this.baseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;
        this.token = token;
        
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(timeoutSeconds, TimeUnit.SECONDS)
                .readTimeout(timeoutSeconds, TimeUnit.SECONDS)
                .writeTimeout(timeoutSeconds, TimeUnit.SECONDS)
                .build();
        
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
    }
    
    /**
     * 设置超时时间
     */
    public ChenYuClient setTimeout(int timeoutSeconds) {
        return new ChenYuClient(this.baseUrl, this.token, timeoutSeconds);
    }
    
    /**
     * 发送POST请求的通用方法
     */
    private <T> T post(String endpoint, Object requestData, TypeReference<APIResponse<T>> responseType) 
            throws ChenYuException {
        
        String url = baseUrl + endpoint;
        
        try {
            // 序列化请求体
            String jsonBody = "";
            if (requestData != null) {
                jsonBody = objectMapper.writeValueAsString(requestData);
            }
            
            RequestBody body = RequestBody.create(
                jsonBody, 
                MediaType.get("application/json; charset=utf-8")
            );
            
            // 创建HTTP请求
            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Authorization", "Bearer " + token)
                    .build();
            
            // 发送请求
            try (Response response = httpClient.newCall(request).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                
                // 检查HTTP状态码
                if (!response.isSuccessful()) {
                    throw new ChenYuHTTPException(
                        response.code(),
                        "HTTP request failed",
                        responseBody
                    );
                }
                
                // 解析JSON响应
                APIResponse<T> apiResponse;
                try {
                    apiResponse = objectMapper.readValue(responseBody, responseType);
                } catch (Exception e) {
                    throw new ChenYuHTTPException(
                        response.code(),
                        "Invalid JSON response: " + e.getMessage(),
                        responseBody,
                        e
                    );
                }
                
                // 检查API响应状态
                if (apiResponse.getCode() != 0) {
                    throw new ChenYuAPIException(
                        apiResponse.getCode(),
                        apiResponse.getMsg()
                    );
                }
                
                return apiResponse.getResult();
            }
            
        } catch (IOException e) {
            throw new ChenYuHTTPException(0, "Request failed: " + e.getMessage(), "", e);
        }
    }
    
    /**
     * 发送POST请求（无返回数据）
     */
    private void postVoid(String endpoint, Object requestData) throws ChenYuException {
        post(endpoint, requestData, new TypeReference<APIResponse<Void>>() {});
    }
    
    /**
     * 查询可用镜像列表
     */
    public PodListResponse getPods(PodListRequest request) throws ChenYuException {
        // 设置默认分页参数
        if (request.getPage() <= 0) {
            request.setPage(1);
        }
        if (request.getPageSize() <= 0 || request.getPageSize() > 50) {
            request.setPageSize(10);
        }
        
        return post("/app/list", request, new TypeReference<APIResponse<PodListResponse>>() {});
    }
    
    /**
     * 创建实例
     */
    public InstanceResponse createInstance(InstanceCreateRequest request) throws ChenYuException {
        if (request.getPodUuid() == null || request.getPodUuid().isEmpty()) {
            throw new ChenYuValidationException("pod_uuid is required");
        }
        if (request.getGpuModelUuid() == null || request.getGpuModelUuid().isEmpty()) {
            throw new ChenYuValidationException("gpu_model_uuid is required");
        }
        
        return post("/app/instance/create", request, new TypeReference<APIResponse<InstanceResponse>>() {});
    }
    
    /**
     * 停止实例
     */
    public void stopInstance(InstanceActionRequest request) throws ChenYuException {
        if (request.getInstanceUuid() == null || request.getInstanceUuid().isEmpty()) {
            throw new ChenYuValidationException("instance_uuid is required");
        }
        
        postVoid("/app/instance/stop", request);
    }
    
    /**
     * 启动实例
     */
    public void startInstance(InstanceActionRequest request) throws ChenYuException {
        if (request.getInstanceUuid() == null || request.getInstanceUuid().isEmpty()) {
            throw new ChenYuValidationException("instance_uuid is required");
        }
        
        postVoid("/app/instance/start", request);
    }
    
    /**
     * 重启实例
     */
    public void restartInstance(InstanceActionRequest request) throws ChenYuException {
        if (request.getInstanceUuid() == null || request.getInstanceUuid().isEmpty()) {
            throw new ChenYuValidationException("instance_uuid is required");
        }
        
        postVoid("/app/instance/restart", request);
    }
    
    /**
     * 查询实例状态
     */
    public InstanceStatusResponse getInstanceStatus(InstanceStatusRequest request) throws ChenYuException {
        if (request.getInstanceUuid() == null || request.getInstanceUuid().isEmpty()) {
            throw new ChenYuValidationException("instance_uuid is required");
        }

        return post("/app/instance/status", request, new TypeReference<APIResponse<InstanceStatusResponse>>() {});
    }

    /**
     * 设置定时关机
     */
    public void setShutdownRegularTime(InstanceShutdownRegularTimeRequest request) throws ChenYuException {
        if (request.getInstanceUuid() == null || request.getInstanceUuid().isEmpty()) {
            throw new ChenYuValidationException("instance_uuid is required");
        }
        if (request.getRegularTime() == null || request.getRegularTime().isEmpty()) {
            throw new ChenYuValidationException("regular_time is required");
        }

        postVoid("/app/instance/scheduled/shutdown", request);
    }

    /**
     * 查询实例列表
     */
    public InstanceListResponse getInstances(InstanceListRequest request) throws ChenYuException {
        // 设置默认分页参数
        if (request.getPage() <= 0) {
            request.setPage(1);
        }
        if (request.getPageSize() <= 0 || request.getPageSize() > 50) {
            request.setPageSize(10);
        }

        return post("/app/instance/list", request, new TypeReference<APIResponse<InstanceListResponse>>() {});
    }

    /**
     * 查询GPU资源
     */
    public GpuModelsResponse getGpuModels(GpuModelsRequest request) throws ChenYuException {
        return post("/gpu/models", request, new TypeReference<APIResponse<GpuModelsResponse>>() {});
    }

    /**
     * 查询余额信息
     */
    public BalanceResponse getBalance(BalanceRequest request) throws ChenYuException {
        return post("/finances/balance", request, new TypeReference<APIResponse<BalanceResponse>>() {});
    }

    /**
     * 查询充值记录
     */
    public RechargeListResponse getRecharge(RechargeListRequest request) throws ChenYuException {
        // 设置默认分页参数
        if (request.getPage() <= 0) {
            request.setPage(1);
        }
        if (request.getPageSize() <= 0 || request.getPageSize() > 50) {
            request.setPageSize(10);
        }

        return post("/finances/recharge", request, new TypeReference<APIResponse<RechargeListResponse>>() {});
    }

    /**
     * 查询账单记录
     */
    public BillListResponse getBill(BillListRequest request) throws ChenYuException {
        // 设置默认分页参数
        if (request.getPage() <= 0) {
            request.setPage(1);
        }
        if (request.getPageSize() <= 0 || request.getPageSize() > 50) {
            request.setPageSize(10);
        }

        return post("/finances/bill", request, new TypeReference<APIResponse<BillListResponse>>() {});
    }
}
