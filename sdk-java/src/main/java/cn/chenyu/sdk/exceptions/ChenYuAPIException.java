package cn.chenyu.sdk.exceptions;

/**
 * API业务异常
 */
public class ChenYuAPIException extends ChenYuException {
    
    private final int code;
    private final String apiMessage;
    
    public ChenYuAPIException(int code, String apiMessage) {
        super(String.format("API Error (code %d): %s", code, apiMessage));
        this.code = code;
        this.apiMessage = apiMessage;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getApiMessage() {
        return apiMessage;
    }
}
