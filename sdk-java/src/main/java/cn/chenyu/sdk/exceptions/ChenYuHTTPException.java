package cn.chenyu.sdk.exceptions;

/**
 * HTTP请求异常
 */
public class ChenYuHTTPException extends ChenYuException {
    
    private final int statusCode;
    private final String responseText;
    
    public ChenYuHTTPException(int statusCode, String message, String responseText) {
        super(String.format("HTTP %d: %s", statusCode, message));
        this.statusCode = statusCode;
        this.responseText = responseText;
    }
    
    public ChenYuHTTPException(int statusCode, String message, String responseText, Throwable cause) {
        super(String.format("HTTP %d: %s", statusCode, message), cause);
        this.statusCode = statusCode;
        this.responseText = responseText;
    }
    
    public int getStatusCode() {
        return statusCode;
    }
    
    public String getResponseText() {
        return responseText;
    }
}
