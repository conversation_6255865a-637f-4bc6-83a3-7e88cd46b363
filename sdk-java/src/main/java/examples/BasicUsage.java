package examples;

import cn.chenyu.sdk.ChenYuClient;
import cn.chenyu.sdk.exceptions.*;
import cn.chenyu.sdk.types.*;

/**
 * 晨羽智云 Java SDK - 基本使用示例
 * 
 * 这个示例展示了如何使用晨羽智云Java SDK进行基本操作。
 */
public class BasicUsage {
    
    public static void main(String[] args) {
        // 创建客户端
        ChenYuClient client = new ChenYuClient(
            "https://www.chenyu.cn/api/v1",
            "your-token-here"  // 请替换为您的实际Token
        );
        
        // 设置超时时间（可选）
        client = client.setTimeout(60);
        
        System.out.println("=== 晨羽智云 Java SDK 基本使用示例 ===");
        
        try {
            // 1. 查询可用镜像
            System.out.println("\n1. 查询可用镜像...");
            PodListRequest podRequest = new PodListRequest(1, 5);
            podRequest.setKw("jupyter"); // 搜索关键词
            
            PodListResponse podsResp = client.getPods(podRequest);
            System.out.printf("找到 %d 个镜像%n", podsResp.getTotal());
            for (PodItem pod : podsResp.getItems()) {
                System.out.printf("镜像: %s (%s)%n", pod.getTitle(), pod.getUuid());
            }
            
            // 2. 查询GPU资源（如果有镜像的话）
            if (!podsResp.getItems().isEmpty()) {
                System.out.println("\n2. 查询GPU资源...");
                PodItem selectedPod = podsResp.getItems().get(0);

                GpuModelsRequest gpuRequest = new GpuModelsRequest();
                gpuRequest.setPodUuid(selectedPod.getUuid());

                GpuModelsResponse gpuResp = client.getGpuModels(gpuRequest);
                System.out.printf("找到 %d 个GPU资源%n", gpuResp.getTotal());
                for (GpuModelItem gpu : gpuResp.getItems()) {
                    System.out.printf("GPU: %s, 价格: %s, 可用性: %s%n",
                        gpu.getTitle(), gpu.getPrice(), gpu.getFreeTxt());
                }
            }

            // 3. 查询实例列表
            System.out.println("\n3. 查询实例列表...");
            InstanceListRequest instanceRequest = new InstanceListRequest(1, 5);
            InstanceListResponse instancesResp = client.getInstances(instanceRequest);
            System.out.printf("找到 %d 个实例%n", instancesResp.getTotal());
            for (InstanceItem instance : instancesResp.getInstance()) {
                System.out.printf("实例: %s (%s), 状态: %s%n",
                    instance.getTitle(), instance.getUuid(), instance.getStatusTxt());
            }

            // 4. 查询余额
            System.out.println("\n4. 查询账户余额...");
            BalanceRequest balanceRequest = new BalanceRequest();
            BalanceResponse balanceResp = client.getBalance(balanceRequest);
            System.out.printf("总余额: %s%n", balanceResp.getBalance());
            System.out.printf("算力卡数量: %d%n", balanceResp.getCards().size());
            for (CardResponse card : balanceResp.getCards()) {
                System.out.printf("卡号: %s, 余额: %s, 状态: %s%n",
                    card.getCardNo(), card.getLeaveAmount(), card.getStatusTxt());
            }

            // 5. 查询充值记录
            System.out.println("\n5. 查询充值记录...");
            RechargeListRequest rechargeRequest = new RechargeListRequest(1, 5);
            RechargeListResponse rechargeResp = client.getRecharge(rechargeRequest);
            System.out.printf("找到 %d 条充值记录%n", rechargeResp.getTotal());
            for (RechargeItem recharge : rechargeResp.getItems()) {
                System.out.printf("充值: %s, 渠道: %s, 时间: %s%n",
                    recharge.getAmount(), recharge.getGateway(), recharge.getCreatedAt());
            }

            // 6. 查询账单记录
            System.out.println("\n6. 查询账单记录...");
            BillListRequest billRequest = new BillListRequest(1, 5);
            BillListResponse billResp = client.getBill(billRequest);
            System.out.printf("找到 %d 条账单记录%n", billResp.getTotal());
            for (BillItem bill : billResp.getItems()) {
                System.out.printf("账单: %s, 卡片: %s, 时间: %s%n",
                    bill.getOccurredAmount(), bill.getCardTxt(), bill.getCreatedAt());
            }
            
            System.out.println("\n=== 基本使用示例完成 ===");
            
        } catch (ChenYuHTTPException e) {
            System.out.printf("HTTP请求错误: %s%n", e.getMessage());
            System.out.printf("状态码: %d%n", e.getStatusCode());
            System.out.printf("响应内容: %s%n", e.getResponseText());
        } catch (ChenYuAPIException e) {
            System.out.printf("API错误: %s%n", e.getMessage());
            System.out.printf("错误代码: %d%n", e.getCode());
            System.out.printf("错误信息: %s%n", e.getApiMessage());
        } catch (ChenYuException e) {
            System.out.printf("SDK错误: %s%n", e.getMessage());
        } catch (Exception e) {
            System.out.printf("未知错误: %s%n", e.getMessage());
            e.printStackTrace();
        }
    }
}
