package examples;

import cn.chenyu.sdk.ChenYuClient;
import cn.chenyu.sdk.exceptions.*;
import cn.chenyu.sdk.types.*;

/**
 * 晨羽智云 Java SDK - 基本使用示例
 * 
 * 这个示例展示了如何使用晨羽智云Java SDK进行基本操作。
 */
public class BasicUsage {
    
    public static void main(String[] args) {
        // 创建客户端
        ChenYuClient client = new ChenYuClient(
            "https://www.chenyu.cn/api/v1",
            "your-token-here"  // 请替换为您的实际Token
        );
        
        // 设置超时时间（可选）
        client = client.setTimeout(60);
        
        System.out.println("=== 晨羽智云 Java SDK 基本使用示例 ===");
        
        try {
            // 1. 查询可用镜像
            System.out.println("\n1. 查询可用镜像...");
            PodListRequest podRequest = new PodListRequest(1, 5);
            podRequest.setKw("jupyter"); // 搜索关键词
            
            PodListResponse podsResp = client.getPods(podRequest);
            System.out.printf("找到 %d 个镜像%n", podsResp.getTotal());
            for (PodItem pod : podsResp.getItems()) {
                System.out.printf("镜像: %s (%s)%n", pod.getTitle(), pod.getUuid());
            }
            
            // 2. 查询GPU资源（如果有镜像的话）
            if (!podsResp.getItems().isEmpty()) {
                System.out.println("\n2. 查询GPU资源...");
                PodItem selectedPod = podsResp.getItems().get(0);
                
                // 注意：这里需要实现GpuModelsRequest和相关方法
                /*
                GpuModelsRequest gpuRequest = new GpuModelsRequest();
                gpuRequest.setPodUuid(selectedPod.getUuid());
                
                GpuModelsResponse gpuResp = client.getGpuModels(gpuRequest);
                System.out.printf("找到 %d 个GPU资源%n", gpuResp.getTotal());
                for (GpuModelItem gpu : gpuResp.getItems()) {
                    System.out.printf("GPU: %s, 价格: %s, 可用性: %s%n", 
                        gpu.getTitle(), gpu.getPrice(), gpu.getFreeTxt());
                }
                */
            }
            
            // 3. 查询实例列表
            System.out.println("\n3. 查询实例列表...");
            // 注意：这里需要实现InstanceListRequest和相关方法
            /*
            InstanceListRequest instanceRequest = new InstanceListRequest(1, 5);
            InstanceListResponse instancesResp = client.getInstances(instanceRequest);
            System.out.printf("找到 %d 个实例%n", instancesResp.getTotal());
            for (InstanceItem instance : instancesResp.getInstance()) {
                System.out.printf("实例: %s (%s), 状态: %s%n", 
                    instance.getTitle(), instance.getUuid(), instance.getStatusTxt());
            }
            */
            
            // 4. 查询余额
            System.out.println("\n4. 查询账户余额...");
            // 注意：这里需要实现BalanceRequest和相关方法
            /*
            BalanceRequest balanceRequest = new BalanceRequest();
            BalanceResponse balanceResp = client.getBalance(balanceRequest);
            System.out.printf("算力卡数量: %d%n", balanceResp.getTotal());
            for (BalanceItem card : balanceResp.getItems()) {
                System.out.printf("卡号: %s, 余额: %s, 状态: %s%n", 
                    card.getCardNo(), card.getBalance(), card.getStatusTxt());
            }
            */
            
            System.out.println("\n=== 基本使用示例完成 ===");
            
        } catch (ChenYuHTTPException e) {
            System.out.printf("HTTP请求错误: %s%n", e.getMessage());
            System.out.printf("状态码: %d%n", e.getStatusCode());
            System.out.printf("响应内容: %s%n", e.getResponseText());
        } catch (ChenYuAPIException e) {
            System.out.printf("API错误: %s%n", e.getMessage());
            System.out.printf("错误代码: %d%n", e.getCode());
            System.out.printf("错误信息: %s%n", e.getApiMessage());
        } catch (ChenYuException e) {
            System.out.printf("SDK错误: %s%n", e.getMessage());
        } catch (Exception e) {
            System.out.printf("未知错误: %s%n", e.getMessage());
            e.printStackTrace();
        }
    }
}
