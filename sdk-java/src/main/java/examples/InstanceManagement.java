package examples;

import cn.chenyu.sdk.ChenYuClient;
import cn.chenyu.sdk.exceptions.*;
import cn.chenyu.sdk.types.*;

/**
 * 晨羽智云 Java SDK - 实例管理示例
 * 
 * 这个示例展示了如何使用晨羽智云Java SDK进行实例管理操作。
 */
public class InstanceManagement {
    
    public static void main(String[] args) {
        // 创建客户端
        ChenYuClient client = new ChenYuClient(
            "https://www.chenyu.cn/api/v1",
            "sk-kyhHuxOoAry36cgX1mZTWSWEAet16GsVtz5HNzODql"
        );
        
        System.out.println("=== 实例管理示例 ===");
        
        String instanceUuid = "c1ce8c227f1946918dd87c9d4a7632d0";
        
        try {
            // 1. 查询可用镜像（注释掉的部分，可以根据需要启用）
            /*
            System.out.println("1. 查询可用镜像...");
            PodListRequest podRequest = new PodListRequest(1, 1);
            PodListResponse podsResp = client.getPods(podRequest);
            if (podsResp.getItems().isEmpty()) {
                System.out.println("没有可用的镜像");
                return;
            }
            PodItem selectedPod = podsResp.getItems().get(0);
            System.out.printf("选择镜像: %s (%s)%n", selectedPod.getTitle(), selectedPod.getUuid());
            */
            
            // 2. 查询GPU资源（注释掉的部分，可以根据需要启用）
            /*
            System.out.println("\n2. 查询GPU资源...");
            GpuModelsRequest gpuRequest = new GpuModelsRequest();
            gpuRequest.setPodUuid(selectedPod.getUuid());
            GpuModelsResponse gpuResp = client.getGpuModels(gpuRequest);
            if (gpuResp.getItems().isEmpty()) {
                System.out.println("没有可用的GPU资源");
                return;
            }
            
            GpuModelItem selectedGpu = null;
            for (GpuModelItem gpu : gpuResp.getItems()) {
                if ("充足".equals(gpu.getFreeTxt())) {
                    selectedGpu = gpu;
                    break;
                }
            }
            
            if (selectedGpu == null) {
                System.out.println("没有充足的GPU资源");
                return;
            }
            System.out.printf("选择GPU: %s (%s)%n", selectedGpu.getTitle(), selectedGpu.getUuid());
            */
            
            // 3. 创建实例（注释掉的部分，可以根据需要启用）
            /*
            System.out.println("\n3. 创建实例...");
            InstanceCreateRequest createRequest = new InstanceCreateRequest(
                selectedPod.getUuid(),
                selectedGpu.getUuid()
            );
            createRequest.setAutoStart(1); // 自动启动
            
            InstanceResponse createResp = client.createInstance(createRequest);
            instanceUuid = createResp.getInstance().getUuid();
            System.out.printf("实例创建成功: %s (%s)%n", 
                createResp.getInstance().getTitle(), instanceUuid);
            */
            
            // 4. 等待实例启动（注释掉的部分，可以根据需要启用）
            /*
            System.out.println("\n4. 等待实例启动...");
            for (int i = 0; i < 30; i++) { // 最多等待30次，每次5秒
                try {
                    InstanceStatusRequest statusRequest = new InstanceStatusRequest(instanceUuid);
                    InstanceStatusResponse statusResp = client.getInstanceStatus(statusRequest);
                    System.out.printf("实例状态: %s%n", statusResp.getInstance().getStatusTxt());
                    
                    // 如果实例已经运行，退出循环
                    if (statusResp.getInstance().getStatus() == 2) { // 假设2表示运行中
                        System.out.println("实例已启动!");
                        if (statusResp.getInstance().getStartupMaps() != null && 
                            !statusResp.getInstance().getStartupMaps().isEmpty()) {
                            System.out.printf("访问信息: %s%n", statusResp.getInstance().getStartupMaps());
                        }
                        break;
                    }
                    
                    Thread.sleep(5000);
                } catch (ChenYuAPIException e) {
                    System.out.printf("查询实例状态失败: %s%n", e.getMessage());
                    continue;
                }
            }
            */
            
            // 5. 重启实例（注释掉的部分，可以根据需要启用）
            /*
            System.out.println("重启实例...");
            try {
                InstanceActionRequest restartRequest = new InstanceActionRequest(instanceUuid);
                client.restartInstance(restartRequest);
                System.out.println("实例重启成功");
            } catch (ChenYuAPIException e) {
                System.out.printf("重启实例失败: %s%n", e.getMessage());
            }
            */
            
            // 6. 演示实例操作
            // System.out.println("\n6. 演示实例操作...");
            
            // 停止并销毁实例（注释掉的部分，可以根据需要启用）
            /*
            System.out.println("停止实例...");
            try {
                InstanceActionRequest stopRequest = new InstanceActionRequest(instanceUuid);
                client.stopInstance(stopRequest);
                System.out.println("实例停止成功");
            } catch (ChenYuAPIException e) {
                System.out.printf("停止实例失败: %s%n", e.getMessage());
            }
            
            // 等待一段时间
            Thread.sleep(10000);
            */
            
            // 启动实例
            System.out.println("启动实例...");
            try {
                InstanceActionRequest startRequest = new InstanceActionRequest(instanceUuid);
                client.startInstance(startRequest);
                System.out.println("实例启动成功");
            } catch (ChenYuAPIException e) {
                System.out.printf("启动实例失败: %s%n", e.getMessage());
            }
            
            System.out.printf("%n实例管理示例完成。实例UUID: %s%n", instanceUuid);
            System.out.println("请在控制台中手动删除测试实例。");
            
        } catch (ChenYuHTTPException e) {
            System.out.printf("HTTP请求错误: %s%n", e.getMessage());
            System.out.printf("状态码: %d%n", e.getStatusCode());
            System.out.printf("响应内容: %s%n", e.getResponseText());
        } catch (ChenYuAPIException e) {
            System.out.printf("API错误: %s%n", e.getMessage());
            System.out.printf("错误代码: %d%n", e.getCode());
            System.out.printf("错误信息: %s%n", e.getApiMessage());
        } catch (ChenYuException e) {
            System.out.printf("SDK错误: %s%n", e.getMessage());
        } catch (Exception e) {
            System.out.printf("未知错误: %s%n", e.getMessage());
            e.printStackTrace();
        }
    }
}
