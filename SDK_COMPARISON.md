# 晨羽智云 SDK 对比文档

本文档对比了Go、Python和Java三个版本的晨羽智云SDK，确保功能完全一致。

## 功能对比表

| 功能分类 | 方法名 | Go SDK | Python SDK | Java SDK | 说明 |
|---------|--------|--------|------------|----------|------|
| **财务管理** | | | | | |
| 查询余额 | GetBalance | ✅ | ✅ get_balance | ✅ getBalance | 查询账户余额和算力卡信息 |
| 查询充值记录 | GetRecharge | ✅ | ✅ get_recharge | ✅ getRecharge | 分页查询充值记录 |
| 查询账单记录 | GetBill | ✅ | ✅ get_bill | ✅ getBill | 分页查询账单记录 |
| **镜像管理** | | | | | |
| 查询镜像列表 | GetPods | ✅ | ✅ get_pods | ✅ getPods | 分页查询可用镜像 |
| **GPU资源** | | | | | |
| 查询GPU资源 | GetGpuModels | ✅ | ✅ get_gpu_models | ✅ getGpuModels | 查询可用GPU资源 |
| **实例管理** | | | | | |
| 创建实例 | CreateInstance | ✅ | ✅ create_instance | ✅ createInstance | 创建新的AI实例 |
| 查询实例列表 | GetInstances | ✅ | ✅ get_instances | ✅ getInstances | 分页查询实例列表 |
| 查询实例状态 | GetInstanceStatus | ✅ | ✅ get_instance_status | ✅ getInstanceStatus | 查询单个实例状态 |
| **实例操作** | | | | | |
| 启动实例 | StartInstance | ✅ | ✅ start_instance | ✅ startInstance | 启动指定实例 |
| 停止实例 | StopInstance | ✅ | ✅ stop_instance | ✅ stopInstance | 停止指定实例 |
| 重启实例 | RestartInstance | ✅ | ✅ restart_instance | ✅ restartInstance | 重启指定实例 |
| 设置定时关机 | SetShutdownRegularTime | ✅ | ✅ set_shutdown_regular_time | ✅ setShutdownRegularTime | 设置实例定时关机 |

## 数据类型对比

### 请求类型

| 类型 | Go | Python | Java |
|------|----|---------|----- |
| 分页请求 | PaginationRequest | PaginationRequest | PaginationRequest |
| 余额查询 | BalanceRequest | BalanceRequest | BalanceRequest |
| 镜像列表查询 | PodListRequest | PodListRequest | PodListRequest |
| GPU资源查询 | GpuModelsRequest | GpuModelsRequest | GpuModelsRequest |
| 实例创建 | InstanceCreateRequest | InstanceCreateRequest | InstanceCreateRequest |
| 实例操作 | InstanceActionRequest | InstanceActionRequest | InstanceActionRequest |
| 实例状态查询 | InstanceStatusRequest | InstanceStatusRequest | InstanceStatusRequest |
| 实例列表查询 | InstanceListRequest | InstanceListRequest | InstanceListRequest |
| 定时关机设置 | InstanceShutdownRegularTimeRequest | InstanceShutdownRegularTimeRequest | InstanceShutdownRegularTimeRequest |
| 充值记录查询 | RechargeListRequest | RechargeListRequest | RechargeListRequest |
| 账单查询 | BillListRequest | BillListRequest | BillListRequest |

### 响应类型

| 类型 | Go | Python | Java |
|------|----|---------|----- |
| 分页响应 | PaginationResponse | PaginationResponse | PaginationResponse |
| 余额响应 | BalanceResponse | BalanceResponse | BalanceResponse |
| 算力卡信息 | CardResponse | CardResponse | CardResponse |
| 镜像列表响应 | PodListResponse | PodListResponse | PodListResponse |
| 镜像项 | PodItem | PodItem | PodItem |
| GPU资源响应 | GpuModelsResponse | GpuModelsResponse | GpuModelsResponse |
| GPU资源项 | GpuModelItem | GpuModelItem | GpuModelItem |
| 实例响应 | InstanceResponse | InstanceResponse | InstanceResponse |
| 实例项 | InstanceItem | InstanceItem | InstanceItem |
| 实例状态响应 | InstanceStatusResponse | InstanceStatusResponse | InstanceStatusResponse |
| 实例状态项 | InstanceStatusItem | InstanceStatusItem | InstanceStatusItem |
| 实例列表响应 | InstanceListResponse | InstanceListResponse | InstanceListResponse |
| 充值记录响应 | RechargeListResponse | RechargeListResponse | RechargeListResponse |
| 充值记录项 | RechargeItem | RechargeItem | RechargeItem |
| 账单响应 | BillListResponse | BillListResponse | BillListResponse |
| 账单项 | BillItem | BillItem | BillItem |

## 异常处理对比

| 异常类型 | Go | Python | Java |
|---------|----|---------|----- |
| 基础异常 | error | ChenYuError | ChenYuException |
| HTTP异常 | error | ChenYuHTTPError | ChenYuHTTPException |
| API异常 | error | ChenYuAPIError | ChenYuAPIException |
| 参数验证异常 | error | ChenYuValidationError | ChenYuValidationException |

## 客户端特性对比

| 特性 | Go | Python | Java |
|------|----|---------|----- |
| HTTP客户端 | net/http | requests | OkHttp |
| JSON处理 | encoding/json | json | Jackson |
| 超时设置 | ✅ | ✅ | ✅ |
| 错误处理 | ✅ | ✅ | ✅ |
| 类型安全 | ✅ | ✅ (dataclass + 类型提示) | ✅ |
| 文档支持 | ✅ | ✅ (docstring) | ✅ (javadoc) |

## 示例代码对比

### 创建客户端

**Go:**
```go
client := client.NewClient("https://api.chenyu.cn/api/v1", "token")
client.SetTimeout(60 * time.Second)
```

**Python:**
```python
client = ChenYuClient("https://api.chenyu.cn/api/v1", "token")
client.set_timeout(60)
```

**Java:**
```java
ChenYuClient client = new ChenYuClient("https://api.chenyu.cn/api/v1", "token");
client = client.setTimeout(60);
```

### 查询镜像列表

**Go:**
```go
resp, err := client.GetPods(&types.PodListRequest{
    PaginationRequest: types.PaginationRequest{Page: 1, PageSize: 10},
    Kw: "jupyter",
})
```

**Python:**
```python
resp = client.get_pods(PodListRequest(
    page=1, page_size=10, kw="jupyter"
))
```

**Java:**
```java
PodListRequest request = new PodListRequest(1, 10);
request.setKw("jupyter");
PodListResponse resp = client.getPods(request);
```

## 总结

三个版本的SDK在功能上完全一致，都包含了：

1. **完整的API覆盖**：所有12个主要API方法
2. **一致的数据结构**：相同的请求/响应类型
3. **统一的错误处理**：分层的异常体系
4. **相同的功能特性**：超时设置、类型安全、文档支持
5. **对应的示例代码**：基本使用和实例管理示例

每个SDK都遵循各自语言的最佳实践和惯例，同时保持API的一致性。
